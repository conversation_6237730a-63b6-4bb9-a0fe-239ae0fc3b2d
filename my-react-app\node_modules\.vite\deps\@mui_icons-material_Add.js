"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-AWFNV5AW.js";
import "./chunk-C7YCGC3Y.js";
import "./chunk-EQCCHGRT.js";
import {
  require_jsx_runtime
} from "./chunk-MJNCUEZK.js";
import "./chunk-UGC3UZ7L.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/icons-material/esm/Add.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Add_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"
}), "Add");
export {
  Add_default as default
};
//# sourceMappingURL=@mui_icons-material_Add.js.map
