import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  TextField,
  Button,
  Grid,
  Paper,
  IconButton,
  useMediaQuery,
  CssBaseline,
  FormControlLabel,
  FormControl,
  FormLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import Autocomplete from '@mui/material/Autocomplete';
import { createTheme, ThemeProvider, styled } from '@mui/material/styles';
import dayjs from 'dayjs';

// Define the custom theme outside the component to avoid re-creation on re-renders
const theme = createTheme({
  palette: {
    primary: {
      main: '#2EC0CB', // Your specified teal theme color
    },
    secondary: {
      main: '#6c757d', // A neutral grey for contrast
    },
    success: {
      main: '#28a745', // Green for completed tasks
      light: '#d4edda',
      contrastText: '#155724',
    },
    info: {
      main: '#17a2b8', // Blue for edit button
    },
    error: {
      main: '#dc3545', // Red for delete button
    },
    background: {
      default: '#f4f6f8', // Light background color for the page
      paper: '#ffffff', // Default background for Paper components
    },
  },
  typography: {
    fontFamily: 'Roobert, "Inter", sans-serif',
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        body {
          font-family: 'Roobert', "Inter", sans-serif;
          background-color: #f4f6f8; /* Explicitly set a light background for the body */
        }
      `,
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
          },
        },
      },
    },
  },
});

// Mock data for dropdowns and tasks
const projects = ['IOCS', 'SWServices', 'Total-Exp', 'Tru-North'];
const taskTypes = ['Project Management', 'Training', 'Meeting', 'Documentation'];
const currentStatuses = ['Discovery Phase', 'Not Yet Assigned', 'On Hold', 'Waiting for Customer Approval', 'Waiting for Internal Approval'];
const taskStatuses = ['Work in Progress', 'On Hold', 'Completed', 'Request for PCD Change Approval', 'Request for Closure Approval'];
const assignedToUsers = [
  'admin', 'Bibhash Kanti Roy', 'Bryce Connors', 'David Campbell',
  'John Talbert', 'Nikil Ram D', 'Niraj kumar Mishra', 'Prasad H',
  'Ravi Tomar', 'Sreekrishna Narayana', 'Stuart Mckay', 'Thomas Remmel',
  'Vishwanath A.B',
];
const repeatFrequencies = ['Daily', 'Weekly', 'Monthly', 'Yearly'];

// Mock attachment data (for display purposes)
const mockAttachments = [
  { id: 1, fileName: 'ProjectPlan.pdf', description: 'Initial project plan', uploadedBy: 'admin', uploadedDate: '2024-06-15' },
  { id: 2, fileName: 'RequirementsDoc.docx', description: 'Detailed requirements', uploadedBy: 'John Talbert', uploadedDate: '2024-06-20' },
];


// Main TaskPage component
function TaskPage() {
  const currentTheme = theme;
  const isMobile = useMediaQuery(currentTheme.breakpoints.down('sm'));

  const [tasks, setTasks] = useState([]);
  const [newTask, setNewTask] = useState({
    id: null,
    taskId: '',
    taskName: '',
    taskDescription: '',
    project: null,
    division: '',
    customer: '',
    taskType: null,
    currentStatus: null,
    taskStatus: null,
    assignedTo: null,
    estimatedHours: '',
    actualHours: '',
    taskPCD: '',
    startDate: '',
    endDate: '',
    repeatTask: false,
    repeatFrequency: null,
    comments: '',
    completed: false,
  });

  // Handler for input changes in TextFields
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTask((prev) => ({ ...prev, [name]: value }));
  };

  // Handler for Autocomplete (dropdown) changes
  const handleAutocompleteChange = (name, value) => {
    setNewTask((prev) => ({ ...prev, [name]: value }));
  };

  // Handler for Date TextField changes
  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setNewTask((prev) => ({ ...prev, [name]: value }));
  };

  // Handler for Checkbox changes (for Repeat Task)
  const handleRepeatTaskChange = (e) => {
    const isChecked = e.target.checked;
    setNewTask((prev) => ({
      ...prev,
      repeatTask: isChecked,
      repeatFrequency: isChecked ? prev.repeatFrequency : null, // Clear frequency if unchecked
    }));
  };

  // Handler for adding a new task or updating an existing one
  const handleAddOrUpdateTask = () => {
    if (newTask.taskName.trim() === '') {
      console.log("Task Name cannot be empty.");
      return;
    }

    if (newTask.id) {
      // Update existing task
      setTasks(
        tasks.map((task) =>
          task.id === newTask.id ? { ...task, ...newTask } : task
        )
      );
    } else {
      // Add new task
      setTasks([
        ...tasks,
        { ...newTask, id: Date.now(), completed: false },
      ]);
    }
    handleClearForm(); // Clear the form after adding/updating
  };

  // Handler for deleting a task
  const handleDeleteTask = (id) => {
    setTasks(tasks.filter((task) => task.id !== id));
  };

  // Handler for toggling the completion status of a task
  const handleToggleComplete = (id) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  // Handler for editing a task: populates the form with task details
  const handleEditTask = (task) => {
    // Ensure dates are in 'YYYY-MM-DD' format for TextField type="date"
    setNewTask({
      ...task,
      taskPCD: task.taskPCD ? dayjs(task.taskPCD).format('YYYY-MM-DD') : '',
      startDate: task.startDate ? dayjs(task.startDate).format('YYYY-MM-DD') : '',
      endDate: task.endDate ? dayjs(task.endDate).format('YYYY-MM-DD') : '',
    });
  };

  // Handler to clear the input form
  const handleClearForm = () => {
    setNewTask({
      id: null,
      taskId: '',
      taskName: '',
      taskDescription: '',
      project: null,
      division: '',
      customer: '',
      taskType: null,
      currentStatus: null,
      taskStatus: null,
      assignedTo: null,
      estimatedHours: '',
      actualHours: '',
      taskPCD: '',
      startDate: '',
      endDate: '',
      repeatTask: false,
      repeatFrequency: null,
      comments: '',
      completed: false,
    });
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, backgroundColor: '#f8f9fa', minHeight: '100vh' }}>
      {/* Page Title */}
      <Typography
        variant="h3"
        component="h1"
        gutterBottom
        align="center"
        sx={{
          color: currentTheme.palette.primary.main,
          mb: 4,
          fontWeight: 700,
          fontSize: isMobile ? '2.5rem' : '3.5rem',
          fontFamily: 'Roobert, sans-serif',
        }}
      >
        My Detailed Task Tracker
      </Typography>

      {/* Main Layout Grid */}
      <Grid container spacing={isMobile ? 2 : 4}>

        {/* Left Column - Task Creation Form */}
        <Grid item xs={12} lg={8}>
          <Grid container spacing={isMobile ? 2 : 3} sx={{ mb: 4 }}>

        {/* Section 1: Basic Task Information Card */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: isMobile ? 2 : 4 }}>
            <Typography variant="h5" component="h2" sx={{ mb: 2, color: currentTheme.palette.primary.dark }}>
              Basic Task Information
            </Typography>
            <Grid container spacing={isMobile ? 2 : 3}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Task ID"
                  name="taskId"
                  value={newTask.taskId}
                  onChange={handleInputChange}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Task Name *"
                  name="taskName"
                  value={newTask.taskName}
                  onChange={handleInputChange}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Priority"
                  name="priority"
                  value={newTask.priority || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="High/Medium/Low"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Category"
                  name="category"
                  value={newTask.category || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="Enter category"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Task Description *"
                  name="taskDescription"
                  value={newTask.taskDescription}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Section 2: Project & Organizational Details Card */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: isMobile ? 2 : 4 }}>
            <Typography variant="h5" component="h2" sx={{ mb: 2, color: currentTheme.palette.primary.dark }}>
              Project & Organizational Details
            </Typography>
            <Grid container spacing={isMobile ? 2 : 3}>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={projects}
                  value={newTask.project}
                  onChange={(event, newValue) => handleAutocompleteChange('project', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Project *" variant="outlined" />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Division"
                  name="division"
                  value={newTask.division}
                  onChange={handleInputChange}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Customer"
                  name="customer"
                  value={newTask.customer}
                  onChange={handleInputChange}
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Department"
                  name="department"
                  value={newTask.department || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="Enter department"
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Section 3: Task Classification & Assignment Card */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: isMobile ? 2 : 4 }}>
            <Typography variant="h5" component="h2" sx={{ mb: 2, color: currentTheme.palette.primary.dark }}>
              Task Classification & Assignment
            </Typography>
            <Grid container spacing={isMobile ? 2 : 3}>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={taskTypes}
                  value={newTask.taskType}
                  onChange={(event, newValue) => handleAutocompleteChange('taskType', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Task Type *" variant="outlined" />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={currentStatuses}
                  value={newTask.currentStatus}
                  onChange={(event, newValue) => handleAutocompleteChange('currentStatus', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Current Status *" variant="outlined" />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={taskStatuses}
                  value={newTask.taskStatus}
                  onChange={(event, newValue) => handleAutocompleteChange('taskStatus', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Task Status" variant="outlined" />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Autocomplete
                  fullWidth
                  options={assignedToUsers}
                  value={newTask.assignedTo}
                  onChange={(event, newValue) => handleAutocompleteChange('assignedTo', newValue)}
                  renderInput={(params) => (
                    <TextField {...params} label="Assigned To" variant="outlined" />
                  )}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Section 4: Scheduling & Effort Card */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: isMobile ? 2 : 4 }}>
            <Typography variant="h5" component="h2" sx={{ mb: 2, color: currentTheme.palette.primary.dark }}>
              Scheduling & Effort
            </Typography>
            <Grid container spacing={isMobile ? 2 : 3}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Estimated Hours (hh:mm) *"
                  name="estimatedHours"
                  value={newTask.estimatedHours}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="HH:MM"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Actual Hours"
                  name="actualHours"
                  value={newTask.actualHours}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="HH:MM"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Task PCD *"
                  name="taskPCD"
                  type="date"
                  value={newTask.taskPCD}
                  onChange={handleDateChange}
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Start Date"
                  name="startDate"
                  type="date"
                  value={newTask.startDate}
                  onChange={handleDateChange}
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="End Date"
                  name="endDate"
                  type="date"
                  value={newTask.endDate}
                  onChange={handleDateChange}
                  variant="outlined"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={newTask.repeatTask}
                      onChange={handleRepeatTaskChange}
                      name="repeatTask"
                      color="primary"
                    />
                  }
                  label="Repeat Task"
                />
              </Grid>
              {newTask.repeatTask && ( // Conditionally render Repeat Frequency
                <Grid item xs={12} sm={6} md={3}>
                  <Autocomplete
                    fullWidth
                    options={repeatFrequencies}
                    value={newTask.repeatFrequency}
                    onChange={(event, newValue) => handleAutocompleteChange('repeatFrequency', newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Repeat Frequency" variant="outlined" />
                    )}
                  />
                </Grid>
              )}
            </Grid>
          </Paper>
        </Grid>

        {/* Comments Card - Full Width */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: isMobile ? 2 : 4 }}>
            <Typography variant="h5" component="h2" sx={{ mb: 2, color: currentTheme.palette.primary.dark }}>
              Comments
            </Typography>
            <Grid container spacing={isMobile ? 2 : 3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Comments"
                  name="comments"
                  value={newTask.comments}
                  onChange={handleInputChange}
                  variant="outlined"
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Action Buttons - Placed after all section cards */}
        <Grid item xs={12}>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              flexDirection: isMobile ? 'column' : 'row',
              justifyContent: 'flex-end',
              mt: 2,
            }}
          >
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddOrUpdateTask}
              fullWidth={isMobile}
            >
              {newTask.id ? 'Update Task' : 'Add Task'}
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<RefreshIcon />}
              onClick={handleClearForm}
              fullWidth={isMobile}
            >
              Clear Form
            </Button>
          </Box>
        </Grid>
          </Grid> {/* End of form sections grid */}
        </Grid> {/* End of left column */}

        {/* Right Column - Dashboard Cards */}
        <Grid item xs={12} lg={4}>
          <Grid container spacing={isMobile ? 2 : 3}>

            {/* Today's Tasks Card */}
            <Grid item xs={12}>
              <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" component="h3" sx={{ mb: 2, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center' }}>
                  <CalendarTodayIcon sx={{ mr: 1 }} />
                  Today's Tasks
                </Typography>
                {tasks.filter(task => {
                  const today = dayjs().format('YYYY-MM-DD');
                  return task.startDate === today || task.taskPCD === today;
                }).length === 0 ? (
                  <Typography variant="body2" color="textSecondary">
                    No tasks scheduled for today
                  </Typography>
                ) : (
                  <Box>
                    {tasks.filter(task => {
                      const today = dayjs().format('YYYY-MM-DD');
                      return task.startDate === today || task.taskPCD === today;
                    }).slice(0, 3).map((task) => (
                      <Box key={task.id} sx={{ mb: 1, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                        <Typography variant="body2" fontWeight="bold">{task.taskName}</Typography>
                        <Typography variant="caption" color="textSecondary">{task.project?.label || 'No Project'}</Typography>
                      </Box>
                    ))}
                  </Box>
                )}
              </Paper>
            </Grid>

            {/* Priority Breakdown Card */}
            <Grid item xs={12}>
              <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" component="h3" sx={{ mb: 2, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center' }}>
                  <PriorityHighIcon sx={{ mr: 1 }} />
                  Priority Breakdown
                </Typography>
                <Box>
                  {['High', 'Medium', 'Low'].map((priority) => {
                    const count = tasks.filter(task => task.priority === priority && !task.completed).length;
                    const color = priority === 'High' ? '#f44336' : priority === 'Medium' ? '#ff9800' : '#4caf50';
                    return (
                      <Box key={priority} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box sx={{ width: 12, height: 12, backgroundColor: color, borderRadius: '50%', mr: 1 }} />
                          <Typography variant="body2">{priority} Priority</Typography>
                        </Box>
                        <Typography variant="body2" fontWeight="bold">{count}</Typography>
                      </Box>
                    );
                  })}
                </Box>
              </Paper>
            </Grid>

            {/* Pending Tasks by Project Card */}
            <Grid item xs={12}>
              <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" component="h3" sx={{ mb: 2, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center' }}>
                  <AssignmentIcon sx={{ mr: 1 }} />
                  Pending by Project
                </Typography>
                <Box>
                  {projects.slice(0, 4).map((project) => {
                    const pendingCount = tasks.filter(task => task.project?.value === project.value && !task.completed).length;
                    return (
                      <Box key={project.value} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {project.label}
                        </Typography>
                        <Typography variant="body2" fontWeight="bold" color={pendingCount > 0 ? 'error.main' : 'success.main'}>
                          {pendingCount}
                        </Typography>
                      </Box>
                    );
                  })}
                </Box>
              </Paper>
            </Grid>

            {/* Urgent Tasks Card */}
            <Grid item xs={12}>
              <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" component="h3" sx={{ mb: 2, color: '#f44336', display: 'flex', alignItems: 'center' }}>
                  <WarningIcon sx={{ mr: 1 }} />
                  Urgent Tasks
                </Typography>
                {tasks.filter(task => {
                  const dueDate = dayjs(task.taskPCD);
                  const today = dayjs();
                  return !task.completed && dueDate.isValid() && dueDate.diff(today, 'days') <= 2;
                }).length === 0 ? (
                  <Typography variant="body2" color="textSecondary">
                    No urgent tasks
                  </Typography>
                ) : (
                  <Box>
                    {tasks.filter(task => {
                      const dueDate = dayjs(task.taskPCD);
                      const today = dayjs();
                      return !task.completed && dueDate.isValid() && dueDate.diff(today, 'days') <= 2;
                    }).slice(0, 3).map((task) => (
                      <Box key={task.id} sx={{ mb: 1, p: 1, backgroundColor: '#ffebee', borderRadius: 1, border: '1px solid #ffcdd2' }}>
                        <Typography variant="body2" fontWeight="bold" color="error.main">{task.taskName}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          Due: {dayjs(task.taskPCD).format('MMM DD, YYYY')}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                )}
              </Paper>
            </Grid>

            {/* Quick Stats Card */}
            <Grid item xs={12}>
              <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
                <Typography variant="h6" component="h3" sx={{ mb: 2, color: currentTheme.palette.primary.dark, display: 'flex', alignItems: 'center' }}>
                  <BarChartIcon sx={{ mr: 1 }} />
                  Quick Stats
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#e3f2fd', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="primary.main">
                        {tasks.length}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Total Tasks</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#e8f5e8', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {tasks.filter(task => task.completed).length}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Completed</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#fff3e0', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="warning.main">
                        {tasks.filter(task => !task.completed).length}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Pending</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ textAlign: 'center', p: 1, backgroundColor: '#fce4ec', borderRadius: 1 }}>
                      <Typography variant="h4" fontWeight="bold" color="error.main">
                        {Math.round(tasks.filter(task => task.completed).length / Math.max(tasks.length, 1) * 100)}%
                      </Typography>
                      <Typography variant="caption" color="textSecondary">Progress</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

          </Grid>
        </Grid> {/* End of right column */}
      </Grid> {/* End of main layout grid */}

      {/* Attachment Details Section */}
      <Paper elevation={3} sx={{ p: isMobile ? 2 : 4, mb: 4 }}>
        <Typography variant="h5" component="h3" gutterBottom sx={{ mb: 2 }}>
          Attachment Details
        </Typography>
        {mockAttachments.length === 0 ? (
          <Typography variant="body1" color="textSecondary">
            No attachments available.
          </Typography>
        ) : (
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Sl. No</TableCell>
                  <TableCell>Select</TableCell>
                  <TableCell>View</TableCell>
                  <TableCell>File Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Uploaded By</TableCell>
                  <TableCell>Uploaded Date</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockAttachments.map((attachment, index) => (
                  <TableRow key={attachment.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell><input type="checkbox" /></TableCell>
                    <TableCell>
                      <IconButton size="small" aria-label="view attachment">
                        <ViewIcon />
                      </IconButton>
                    </TableCell>
                    <TableCell>{attachment.fileName}</TableCell>
                    <TableCell>{attachment.description}</TableCell>
                    <TableCell>{attachment.uploadedBy}</TableCell>
                    <TableCell>{attachment.uploadedDate}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Your Tasks List Section */}
      <Typography
        variant="h4"
        component="h2"
        gutterBottom
        align="center"
        sx={{
          color: currentTheme.palette.primary.main,
          mb: 3,
          fontWeight: 600,
          fontSize: isMobile ? '2rem' : '2.5rem',
          fontFamily: 'Roobert, sans-serif',
        }}
      >
        Your Tasks
      </Typography>

      {tasks.length === 0 ? (
        <Typography variant="h6" align="center" color="textSecondary" sx={{ mt: 5 }}>
          No tasks yet! Add a new task above to get started.
        </Typography>
      ) : (
        <Grid container spacing={isMobile ? 2 : 3}>
          {tasks.map((task) => (
            <Grid item xs={12} sm={6} md={3} key={task.id}>
              <Paper
                elevation={2}
                sx={{
                  p: isMobile ? 2 : 3,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  minHeight: '200px',
                  borderColor: task.completed ? currentTheme.palette.success.main : currentTheme.palette.grey[300],
                  borderWidth: 1,
                  borderStyle: 'solid',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'transform 0.3s, box-shadow 0.3s',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: currentTheme.shadows[6],
                  },
                }}
              >
                <Box>
                  <Typography
                    variant="h6"
                    component="h3"
                    sx={{
                      textDecoration: task.completed ? 'line-through' : 'none',
                      mb: 1,
                      wordBreak: 'break-word',
                    }}
                  >
                    {task.taskName} {task.taskId && `(#${task.taskId})`}
                  </Typography>
                  {task.project && (
                    <Typography variant="body2" color="textSecondary">
                      Project: {task.project}
                    </Typography>
                  )}
                  {task.assignedTo && (
                    <Typography variant="body2" color="textSecondary">
                      Assigned To: {task.assignedTo}
                    </Typography>
                  )}
                  {task.taskType && (
                    <Typography variant="body2" color="textSecondary">
                      Type: {task.taskType}
                    </Typography>
                  )}
                  {task.currentStatus && (
                    <Typography variant="body2" color="textSecondary">
                      Status: {task.currentStatus}
                    </Typography>
                  )}
                  {task.estimatedHours && (
                    <Typography variant="body2" color="textSecondary">
                      Est. Hours: {task.estimatedHours}
                    </Typography>
                  )}
                  {task.taskPCD && (
                    <Typography variant="body2" color="textSecondary">
                      PCD: {task.taskPCD}
                    </Typography>
                  )}
                  {task.startDate && (
                    <Typography variant="body2" color="textSecondary">
                      Start Date: {task.startDate}
                    </Typography>
                  )}
                  {task.endDate && (
                    <Typography variant="body2" color="textSecondary">
                      End Date: {task.endDate}
                    </Typography>
                  )}
                  {task.repeatTask && task.repeatFrequency && (
                    <Typography variant="body2" color="textSecondary">
                      Repeat: {task.repeatFrequency}
                    </Typography>
                  )}
                </Box>
                <Box
                  sx={{
                    mt: 2,
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: 1,
                  }}
                >
                  <IconButton
                    color={task.completed ? 'success' : 'default'}
                    onClick={() => handleToggleComplete(task.id)}
                    aria-label={task.completed ? 'Mark Incomplete' : 'Mark Complete'}
                  >
                    <CheckCircleIcon />
                  </IconButton>
                  <IconButton
                    color="info"
                    onClick={() => handleEditTask(task)}
                    aria-label="Edit Task"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    color="error"
                    onClick={() => handleDeleteTask(task.id)}
                    aria-label="Delete Task"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
                {task.completed && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      backgroundColor: currentTheme.palette.success.light,
                      color: currentTheme.palette.success.contrastText,
                      px: 1.5,
                      py: 0.5,
                      borderBottomLeftRadius: 8,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      textTransform: 'uppercase',
                    }}
                  >
                    Completed
                  </Box>
                )}
              </Paper>
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  );
}

// The main App component that wraps the TaskPage with the ThemeProvider
export default function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <TaskPage />
    </ThemeProvider>
  );
}
