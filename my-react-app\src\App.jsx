import React, { useState } from 'react';
import {
  Container,
  Grid,
  TextField,
  Button,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Checkbox,
  FormControlLabel,
  Paper,
  Typography,
  IconButton,
  Tooltip,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'; // or other adapter
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DataGrid } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

const NewTaskPage = () => {
  const [taskData, setTaskData] = useState({
    taskId: '',
    taskName: '',
    taskDescription: '',
    project: '',
    division: '',
    customer: '',
    taskType: '',
    taskStatus: 'Request for Approval',
    currentStatus: '',
    assignedTo: 'admin',
    estimatedHours: '',
    actualHours: '',
    taskPCD: null, // Date object
    startDate: null, // New
    endDate: null,   // New
    repeatTask: false,
    taskPCT: 'Daily',
    comments: '',
  });

  const [activities, setActivities] = useState([]); // State for sub-tasks/activities
  const [attachments, setAttachments] = useState([]); // State for attachments

  const handleTaskChange = (e) => {
    const { name, value, type, checked } = e.target;
    setTaskData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleDateChange = (name, date) => {
    setTaskData((prev) => ({
      ...prev,
      [name]: date,
    }));
  };

  const handleAddActivity = () => {
    setActivities((prev) => [
      ...prev,
      {
        id: Date.now(), // Unique ID for key prop
        activityName: '',
        description: '',
        estimatedHours: '',
        startDate: null,
        endDate: null,
        assignedTo: '',
      },
    ]);
  };

  const handleDeleteActivity = (id) => {
    setActivities((prev) => prev.filter((activity) => activity.id !== id));
  };

  const handleActivityChange = (id, e) => {
    const { name, value } = e.target;
    setActivities((prev) =>
      prev.map((activity) =>
        activity.id === id ? { ...activity, [name]: value } : activity
      )
    );
  };

  const handleActivityDateChange = (id, name, date) => {
    setActivities((prev) =>
      prev.map((activity) =>
        activity.id === id ? { ...activity, [name]: date } : activity
      )
    );
  };

  const handleSave = () => {
    console.log('Task Data:', taskData);
    console.log('Activities:', activities);
    console.log('Attachments:', attachments);
    // Add API call to save data
  };

  const handleReset = () => {
    setTaskData({
      taskId: '',
      taskName: '',
      taskDescription: '',
      project: '',
      division: '',
      customer: '',
      taskType: '',
      taskStatus: 'Request for Approval',
      currentStatus: '',
      assignedTo: 'admin',
      estimatedHours: '',
      actualHours: '',
      taskPCD: null,
      startDate: null,
      endDate: null,
      repeatTask: false,
      taskPCT: 'Daily',
      comments: '',
    });
    setActivities([]);
    setAttachments([]);
  };

  // DataGrid columns for attachments
  const attachmentColumns = [
    { field: 'id', headerName: 'S.No', width: 70 },
    { field: 'select', headerName: 'Select', width: 90, renderCell: (params) => (<Checkbox />) },
    { field: 'view', headerName: 'View', width: 90, renderCell: (params) => (<Button size="small">View</Button>) },
    { field: 'fileName', headerName: 'File Name', width: 200 },
    { field: 'description', headerName: 'Description', flex: 1 },
    { field: 'uploadedBy', headerName: 'Uploaded By', width: 150 },
    { field: 'uploadedDate', headerName: 'Uploaded Date', width: 180 },
  ];

  // Dummy attachment data (replace with real data)
  const dummyAttachments = [
    { id: 1, fileName: 'Document_A.pdf', description: 'Project Scope', uploadedBy: 'admin', uploadedDate: '2025-07-01' },
    { id: 2, fileName: 'Image_001.png', description: 'UI Mockup', uploadedBy: 'user1', uploadedDate: '2025-07-05' },
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 3 }}>
          New Task
        </Typography>

        <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
          <Grid container spacing={3}>
            {/* Task Details Section */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Task Details</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Task ID"
                name="taskId"
                value={taskData.taskId}
                onChange={handleTaskChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Task Name"
                name="taskName"
                value={taskData.taskName}
                onChange={handleTaskChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Task Description"
                name="taskDescription"
                value={taskData.taskDescription}
                onChange={handleTaskChange}
                multiline
                rows={4}
                required
              />
            </Grid>

            {/* Project & Assignment Details */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Project & Assignment</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>Project</InputLabel>
                <Select
                  name="project"
                  value={taskData.project}
                  label="Project"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Project A">Project A</MenuItem>
                  <MenuItem value="Project B">Project B</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Division</InputLabel>
                <Select
                  name="division"
                  value={taskData.division}
                  label="Division"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Division X">Division X</MenuItem>
                  <MenuItem value="Division Y">Division Y</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Customer"
                name="customer"
                value={taskData.customer}
                onChange={handleTaskChange}
              />
            </Grid>

            {/* Task Type and Status */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Status & Type</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Task Type</InputLabel>
                <Select
                  name="taskType"
                  value={taskData.taskType}
                  label="Task Type"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Development">Development</MenuItem>
                  <MenuItem value="Testing">Testing</MenuItem>
                  <MenuItem value="Documentation">Documentation</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Task Status</InputLabel>
                <Select
                  name="taskStatus"
                  value={taskData.taskStatus}
                  label="Task Status"
                  onChange={handleTaskChange}
                >
                  <MenuItem value="Request for Approval">Request for Approval</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>Current Status</InputLabel>
                <Select
                  name="currentStatus"
                  value={taskData.currentStatus}
                  label="Current Status"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Pending">Pending</MenuItem>
                  <MenuItem value="Approved">Approved</MenuItem>
                  <MenuItem value="Rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Assigned To</InputLabel>
                <Select
                  name="assignedTo"
                  value={taskData.assignedTo}
                  label="Assigned To"
                  onChange={handleTaskChange}
                >
                  <MenuItem value="admin">admin</MenuItem>
                  <MenuItem value="user1">user1</MenuItem>
                  <MenuItem value="user2">user2</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Hours and Dates */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Hours & Dates</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Estimated Hours (hh.mm)"
                name="estimatedHours"
                value={taskData.estimatedHours}
                onChange={handleTaskChange}
                type="text" // Keep as text to allow hh.mm format, validate on submit
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Actual Hours (hh.mm)"
                name="actualHours"
                value={taskData.actualHours}
                onChange={handleTaskChange}
                type="text"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <DatePicker
                label="Task PCD (Planned Completion Date)"
                value={taskData.taskPCD}
                onChange={(date) => handleDateChange('taskPCD', date)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>

            {/* New: Start and End Dates for the overall task */}
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Start Date"
                value={taskData.startDate}
                onChange={(date) => handleDateChange('startDate', date)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <DatePicker
                label="End Date"
                value={taskData.endDate}
                onChange={(date) => handleDateChange('endDate', date)}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>

            {/* Repeat Task */}
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={taskData.repeatTask}
                    onChange={handleTaskChange}
                    name="repeatTask"
                  />
                }
                label="Repeat Task"
              />
            </Grid>
            {taskData.repeatTask && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Repeat Frequency</InputLabel>
                  <Select
                    name="taskPCT"
                    value={taskData.taskPCT}
                    label="Repeat Frequency"
                    onChange={handleTaskChange}
                  >
                    <MenuItem value="Daily">Daily</MenuItem>
                    <MenuItem value="Weekly">Weekly</MenuItem>
                    <MenuItem value="Monthly">Monthly</MenuItem>
                    <MenuItem value="Yearly">Yearly</MenuItem>
                    <MenuItem value="Range">Range</MenuItem> {/* Assuming Range is an option */}
                  </Select>
                </FormControl>
              </Grid>
            )}
            {taskData.repeatTask && taskData.taskPCT === 'Range' && (
              <>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Repeat From Date"
                    value={taskData.repeatFromDate}
                    onChange={(date) => handleDateChange('repeatFromDate', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Repeat To Date"
                    value={taskData.repeatToDate}
                    onChange={(date) => handleDateChange('repeatToDate', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Comments"
                name="comments"
                value={taskData.comments}
                onChange={handleTaskChange}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Project Activities Section (New Functionality) */}
        {taskData.project && ( // Only show if a project is selected
          <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={10}>
                <Typography variant="h6">Project Activities</Typography>
              </Grid>
              <Grid item xs={2} textAlign="right">
                <Button variant="contained" startIcon={<AddIcon />} onClick={handleAddActivity}>
                  Add Activity
                </Button>
              </Grid>
            </Grid>
            {activities.map((activity, index) => (
              <Paper key={activity.id} elevation={1} sx={{ p: 3, mt: 2, mb: 2, borderLeft: '4px solid #1976d2' }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={`Activity ${index + 1} Name`}
                      name="activityName"
                      value={activity.activityName}
                      onChange={(e) => handleActivityChange(activity.id, e)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Estimated Hours"
                      name="estimatedHours"
                      value={activity.estimatedHours}
                      onChange={(e) => handleActivityChange(activity.id, e)}
                      type="text"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Description"
                      name="description"
                      value={activity.description}
                      onChange={(e) => handleActivityChange(activity.id, e)}
                      multiline
                      rows={2}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <DatePicker
                      label="Activity Start Date"
                      value={activity.startDate}
                      onChange={(date) => handleActivityDateChange(activity.id, 'startDate', date)}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <DatePicker
                      label="Activity End Date"
                      value={activity.endDate}
                      onChange={(date) => handleActivityDateChange(activity.id, 'endDate', date)}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Assigned To</InputLabel>
                      <Select
                        name="assignedTo"
                        value={activity.assignedTo}
                        label="Assigned To"
                        onChange={(e) => handleActivityChange(activity.id, e)}
                      >
                        <MenuItem value=""><em>Select...</em></MenuItem>
                        <MenuItem value="user1">user1</MenuItem>
                        <MenuItem value="user2">user2</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} textAlign="right">
                    <Tooltip title="Delete Activity">
                      <IconButton color="error" onClick={() => handleDeleteActivity(activity.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Grid>
                </Grid>
              </Paper>
            ))}
          </Paper>
        )}

        {/* Attachment Details */}
        <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
          <Typography variant="h6" gutterBottom>Attachment Details</Typography>
          <div style={{ height: 300, width: '100%' }}>
            <DataGrid
              rows={dummyAttachments} // Replace with your state for attachments
              columns={attachmentColumns}
              pageSize={5}
              rowsPerPageOptions={[5, 10]}
              checkboxSelection
              disableSelectionOnClick
            />
          </div>
          {/* Add file upload functionality here */}
          <Button variant="outlined" sx={{ mt: 2 }}>Upload File</Button>
        </Paper>

        {/* Action Buttons */}
        <Grid container spacing={2} justifyContent="flex-end">
          <Grid item>
            <Button variant="contained" color="primary" onClick={handleSave}>
              Save
            </Button>
          </Grid>
          <Grid item>
            <Button variant="outlined" color="secondary" onClick={handleReset}>
              Reset
            </Button>
          </Grid>
        </Grid>
      </Container>
    </LocalizationProvider>
  );
};

export default NewTaskPage;