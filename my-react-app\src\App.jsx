import React, { useState } from 'react';
import {
  Container,
  Grid,
  TextField,
  Button,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Checkbox,
  FormControlLabel,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  // Box // Can be used for custom spacing if Grid isn't sufficient
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DataGrid } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

const NewTaskPage = () => {
  const [taskData, setTaskData] = useState({
    taskId: '',
    taskName: '',
    taskDescription: '',
    project: '',
    division: '',
    customer: '',
    taskType: '',
    taskStatus: 'Request for Approval',
    currentStatus: '',
    assignedTo: 'admin',
    estimatedHours: '',
    actualHours: '',
    taskPCD: null,
    startDate: null,
    endDate: null,
    repeatTask: false,
    taskPCT: 'Daily',
    repeatFromDate: null,
    repeatToDate: null,
    comments: '',
  });

  const [activities, setActivities] = useState([]);
  const [attachments, setAttachments] = useState([
    { id: 1, fileName: 'Document_A.pdf', description: 'Project Scope', uploadedBy: 'admin', uploadedDate: '2025-07-01' },
    { id: 2, fileName: 'Image_001.png', description: 'UI Mockup', uploadedBy: 'user1', uploadedDate: '2025-07-05' },
    { id: 3, fileName: 'Presentation_V2.pptx', description: 'Client Pitch', uploadedBy: 'admin', uploadedDate: '2025-06-28' },
  ]);

  const handleTaskChange = (e) => {
    const { name, value, type, checked } = e.target;
    setTaskData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleDateChange = (name, date) => {
    setTaskData((prev) => ({
      ...prev,
      [name]: date,
    }));
  };

  const handleAddActivity = () => {
    setActivities((prev) => [
      ...prev,
      {
        id: Date.now(),
        activityName: '',
        description: '',
        estimatedHours: '',
        startDate: null,
        endDate: null,
        assignedTo: '',
      },
    ]);
  };

  const handleDeleteActivity = (id) => {
    setActivities((prev) => prev.filter((activity) => activity.id !== id));
  };

  const handleActivityChange = (id, e) => {
    const { name, value } = e.target;
    setActivities((prev) =>
      prev.map((activity) =>
        activity.id === id ? { ...activity, [name]: value } : activity
      )
    );
  };

  const handleActivityDateChange = (id, name, date) => {
    setActivities((prev) =>
      prev.map((activity) =>
        activity.id === id ? { ...activity, [name]: date } : activity
      )
    );
  };

  const handleSave = () => {
    console.log('Task Data:', taskData);
    console.log('Activities:', activities);
    console.log('Attachments:', attachments);
    alert('Task data logged to console. In a real app, this would be saved to a database.');
  };

  const handleReset = () => {
    setTaskData({
      taskId: '',
      taskName: '',
      taskDescription: '',
      project: '',
      division: '',
      customer: '',
      taskType: '',
      taskStatus: 'Request for Approval',
      currentStatus: '',
      assignedTo: 'admin',
      estimatedHours: '',
      actualHours: '',
      taskPCD: null,
      startDate: null,
      endDate: null,
      repeatTask: false,
      taskPCT: 'Daily',
      repeatFromDate: null,
      repeatToDate: null,
      comments: '',
    });
    setActivities([]);
  };

  const attachmentColumns = [
    { field: 'id', headerName: 'S.No', width: 70 },
    { field: 'select', headerName: 'Select', width: 90, renderCell: (params) => (<Checkbox />) },
    { field: 'view', headerName: 'View', width: 90, renderCell: (params) => (<Button size="small" variant="text">View</Button>) },
    { field: 'fileName', headerName: 'File Name', width: 200 },
    { field: 'description', headerName: 'Description', flex: 1, minWidth: 150 },
    { field: 'uploadedBy', headerName: 'Uploaded By', width: 150 },
    { field: 'uploadedDate', headerName: 'Uploaded Date', width: 180 },
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 3 }}>
          New Task
        </Typography>

        {/* Main Task Details Section */}
        <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
          {/* Apply alignItems="flex-start" to ensure top alignment of items in the row */}
          <Grid container spacing={3} alignItems="flex-start">

            {/* Task Details Header */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Task Details</Typography>
            </Grid>
            {/* Task ID, Task Name, Task Description */}
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Task ID"
                name="taskId"
                value={taskData.taskId}
                onChange={handleTaskChange}
                required
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Task Name"
                name="taskName"
                value={taskData.taskName}
                onChange={handleTaskChange}
                required
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Task Description"
                name="taskDescription"
                value={taskData.taskDescription}
                onChange={handleTaskChange}
                multiline
                rows={4}
                required
                variant="outlined"
              />
            </Grid>

            {/* Project & Assignment Header */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>Project & Assignment</Typography>
            </Grid>
            {/* Project, Division, Customer */}
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth required variant="outlined">
                <InputLabel id="project-label">Project</InputLabel>
                <Select
                  labelId="project-label"
                  name="project"
                  value={taskData.project}
                  label="Project"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Project A">Project A</MenuItem>
                  <MenuItem value="Project B">Project B</MenuItem>
                  <MenuItem value="Project C">Project C</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="division-label">Division</InputLabel>
                <Select
                  labelId="division-label"
                  name="division"
                  value={taskData.division}
                  label="Division"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Division X">Division X</MenuItem>
                  <MenuItem value="Division Y">Division Y</MenuItem>
                  <MenuItem value="Division Z">Division Z</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Customer"
                name="customer"
                value={taskData.customer}
                onChange={handleTaskChange}
                variant="outlined"
              />
            </Grid>

            {/* Status & Type Header */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>Status & Type</Typography>
            </Grid>
            {/* Task Type, Task Status, Current Status */}
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="task-type-label">Task Type</InputLabel>
                <Select
                  labelId="task-type-label"
                  name="taskType"
                  value={taskData.taskType}
                  label="Task Type"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Development">Development</MenuItem>
                  <MenuItem value="Testing">Testing</MenuItem>
                  <MenuItem value="Documentation">Documentation</MenuItem>
                  <MenuItem value="Meeting">Meeting</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="task-status-label">Task Status</InputLabel>
                <Select
                  labelId="task-status-label"
                  name="taskStatus"
                  value={taskData.taskStatus}
                  label="Task Status"
                  onChange={handleTaskChange}
                >
                  <MenuItem value="Request for Approval">Request for Approval</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                  <MenuItem value="On Hold">On Hold</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth required variant="outlined">
                <InputLabel id="current-status-label">Current Status</InputLabel>
                <Select
                  labelId="current-status-label"
                  name="currentStatus"
                  value={taskData.currentStatus}
                  label="Current Status"
                  onChange={handleTaskChange}
                >
                  <MenuItem value=""><em>Select...</em></MenuItem>
                  <MenuItem value="Pending">Pending</MenuItem>
                  <MenuItem value="Approved">Approved</MenuItem>
                  <MenuItem value="Rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {/* Assigned To */}
            <Grid item xs={12} sm={6} md={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="assigned-to-label">Assigned To</InputLabel>
                <Select
                  labelId="assigned-to-label"
                  name="assignedTo"
                  value={taskData.assignedTo}
                  label="Assigned To"
                  onChange={handleTaskChange}
                >
                  <MenuItem value="admin">admin</MenuItem>
                  <MenuItem value="user1">user1</MenuItem>
                  <MenuItem value="user2">user2</MenuItem>
                  <MenuItem value="user3">user3</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Hours & Dates Header */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>Hours & Dates</Typography>
            </Grid>
            {/* Estimated, Actual, Task PCD */}
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Estimated Hours (hh.mm)"
                name="estimatedHours"
                value={taskData.estimatedHours}
                onChange={handleTaskChange}
                type="text"
                placeholder="e.g., 8.30 (8 hours 30 min)"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Actual Hours (hh.mm)"
                name="actualHours"
                value={taskData.actualHours}
                onChange={handleTaskChange}
                type="text"
                placeholder="e.g., 7.45 (7 hours 45 min)"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <DatePicker
                label="Task PCD (Planned Completion Date)"
                value={taskData.taskPCD}
                onChange={(date) => handleDateChange('taskPCD', date)}
                slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
              />
            </Grid>

            {/* New: Overall Task Start and End Dates */}
            <Grid item xs={12} sm={6} md={6}>
              <DatePicker
                label="Start Date"
                value={taskData.startDate}
                onChange={(date) => handleDateChange('startDate', date)}
                slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <DatePicker
                label="End Date"
                value={taskData.endDate}
                onChange={(date) => handleDateChange('endDate', date)}
                slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
              />
            </Grid>

            {/* Repeat Task Checkbox and Frequency */}
            <Grid item xs={12} sm={6} md={4}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={taskData.repeatTask}
                    onChange={handleTaskChange}
                    name="repeatTask"
                  />
                }
                label="Repeat Task"
              />
            </Grid>
            {taskData.repeatTask && (
              <Grid item xs={12} sm={6} md={4}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel id="repeat-frequency-label">Repeat Frequency</InputLabel>
                  <Select
                    labelId="repeat-frequency-label"
                    name="taskPCT"
                    value={taskData.taskPCT}
                    label="Repeat Frequency"
                    onChange={handleTaskChange}
                  >
                    <MenuItem value="Daily">Daily</MenuItem>
                    <MenuItem value="Weekly">Weekly</MenuItem>
                    <MenuItem value="Monthly">Monthly</MenuItem>
                    <MenuItem value="Yearly">Yearly</MenuItem>
                    <MenuItem value="Range">Range</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            {taskData.repeatTask && taskData.taskPCT === 'Range' && (
              <>
                <Grid item xs={12} sm={6} md={4}>
                  <DatePicker
                    label="Repeat From Date"
                    value={taskData.repeatFromDate}
                    onChange={(date) => handleDateChange('repeatFromDate', date)}
                    slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <DatePicker
                    label="Repeat To Date"
                    value={taskData.repeatToDate}
                    onChange={(date) => handleDateChange('repeatToDate', date)}
                    slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
                  />
                </Grid>
              </>
            )}

            {/* Comments Field */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Comments"
                name="comments"
                value={taskData.comments}
                onChange={handleTaskChange}
                multiline
                rows={3}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Project Activities Section */}
        {taskData.project && (
          <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
            <Grid container spacing={2} alignItems="flex-start"> {/* Align activities to top */}
              <Grid item xs={10}>
                <Typography variant="h6">Project Activities</Typography>
              </Grid>
              <Grid item xs={2} textAlign="right">
                <Button variant="contained" startIcon={<AddIcon />} onClick={handleAddActivity}>
                  Add Activity
                </Button>
              </Grid>
            </Grid>

            {activities.map((activity, index) => (
              <Paper key={activity.id} elevation={1} sx={{ p: 3, mt: 2, mb: 2, borderLeft: '4px solid #1976d2' }}>
                <Grid container spacing={2} alignItems="flex-start"> {/* Align sub-activity fields to top */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={`Activity ${index + 1} Name`}
                      name="activityName"
                      value={activity.activityName}
                      onChange={(e) => handleActivityChange(activity.id, e)}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Estimated Hours"
                      name="estimatedHours"
                      value={activity.estimatedHours}
                      onChange={(e) => handleActivityChange(activity.id, e)}
                      type="text"
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Description"
                      name="description"
                      value={activity.description}
                      onChange={(e) => handleActivityChange(activity.id, e)}
                      multiline
                      rows={2}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <DatePicker
                      label="Activity Start Date"
                      value={activity.startDate}
                      onChange={(date) => handleActivityDateChange(activity.id, 'startDate', date)}
                      slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <DatePicker
                      label="Activity End Date"
                      value={activity.endDate}
                      onChange={(date) => handleActivityDateChange(activity.id, 'endDate', date)}
                      slotProps={{ textField: { fullWidth: true, variant: 'outlined' } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel id={`activity-${activity.id}-assigned-to-label`}>Assigned To</InputLabel>
                      <Select
                        labelId={`activity-${activity.id}-assigned-to-label`}
                        name="assignedTo"
                        value={activity.assignedTo}
                        label="Assigned To"
                        onChange={(e) => handleActivityChange(activity.id, e)}
                      >
                        <MenuItem value=""><em>Select...</em></MenuItem>
                        <MenuItem value="user1">user1</MenuItem>
                        <MenuItem value="user2">user2</MenuItem>
                        <MenuItem value="user3">user3</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} textAlign="right">
                    <Tooltip title="Delete Activity">
                      <IconButton color="error" onClick={() => handleDeleteActivity(activity.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Grid>
                </Grid>
              </Paper>
            ))}
          </Paper>
        )}

        {/* Attachment Details Section */}
        <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
          <Typography variant="h6" gutterBottom>Attachment Details</Typography>
          <div style={{ height: 300, width: '100%' }}>
            <DataGrid
              rows={attachments}
              columns={attachmentColumns}
              pageSizeOptions={[5, 10, 25]}
              initialState={{
                pagination: {
                  paginationModel: { pageSize: 5 },
                },
              }}
              checkboxSelection
              disableRowSelectionOnClick
            />
          </div>
          <Button variant="outlined" sx={{ mt: 2 }}>Upload File</Button>
        </Paper>

        {/* Action Buttons */}
        <Grid container spacing={2} justifyContent="flex-end}>
          <Grid item>
            <Button variant="contained" color="primary" onClick={handleSave}>
              Save
            </Button>
          </Grid>
          <Grid item>
            <Button variant="outlined" color="secondary" onClick={handleReset}>
              Reset
            </Button>
          </Grid>
        </Grid>
      </Container>
    </LocalizationProvider>
  );
};

export default NewTaskPage;